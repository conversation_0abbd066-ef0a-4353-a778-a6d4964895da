from flask import Flask, request, jsonify, send_from_directory
import os
import json
import logging
from google import genai
from google.genai import types
from datetime import datetime
from config import config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Load configuration
config_name = os.environ.get('FLASK_CONFIG', 'default')
app.config.from_object(config[config_name])

# Configuration variables
MEMORY_FILE = app.config['MEMORY_FILE']
HISTORY_FILE = app.config['HISTORY_FILE']
API_KEY = app.config['GOOGLE_API_KEY']

# Emotional triggers
emotional_triggers = [
    "مرعوب", "خايف", "قلقان", "متوتر", "هموت", "بحس بضيق", 
    "زعلان", "حزين", "مخنوق", "مجنون", "حاسس بضيق"
]

# First aid guides
first_aid_guides = {
    "حروق": "🔥 في حالة الحروق:\n1. بعدّي المياه الباردة على مكان الحرق لمدة 10 دقايق.\n2. متحطش ثلج أو معجون أسنان.\n3. لو الحرق شديد أو فيه فقاعات، لازم تروح طوارئ.\n\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.",
    "غرق": "🌊 في حالة الغرق:\n1. طلع الشخص من المية فورًا.\n2. لو مش بيتنفس، ابتدي **الإنعاش القلبي الرئوي (CPR)**.\n3. كلم الإسعاف فورًا.\n\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.",
    "إغماء": "😵 في حالة الإغماء:\n1. مدد الشخص على الأرض.\n2. ارفع رجله شوية.\n3. لو ما فاقش خلال دقيقة، اتصل بالإسعاف.\n\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.",
    "اختناق": "😮‍💨 في حالة الاختناق:\n1. اسأل الشخص لو يقدر يتكلم أو يسعل.\n2. لو مش قادر، ابتدي **مناورة هيمليخ**.\n3. لو فقد الوعي، ابتدي **الإنعاش القلبي الرئوي (CPR)** واتصل بالإسعاف.\n\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.",
    "جروح": "🩸 في حالة الجروح:\n1. نظّف الجرح بمياه جارية.\n2. حط شاش معقم واضغط عليه.\n3. لو الجرح عميق أو بينزف كتير، لازم تروح طوارئ.\n\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.",
    "تسمم": "☠️ في حالة التسمم:\n1. متحاولش تخلي الشخص يرجع.\n2. اتصل بمركز السموم فورًا.\n3. اعرف نوع المادة اللي اتسمم منها لو أمكن.\n\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.",
    "صرع": "⚡ في حالة نوبة صرع:\n1. ابعد أي حاجة ممكن تأذيه.\n2. متقيدهوش، وسيبه لحد ما النوبة تخلص.\n3. لو النوبة استمرت أكتر من ٥ دقايق، اتصل بالإسعاف.\n\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.",
    "ضربات الرأس": "🧠 في حالة ضربة رأس:\n1. راقب الشخص لو فيه قيء، فقدان وعي، أو صداع شديد.\n2. لو فيه أي من دول، لازم يروح طوارئ.\n3. متديهش أدوية بدون استشارة طبيب.\n\n⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.",
    "cpr": "❤️ إزاي تعمل الإنعاش القلبي الرئوي (CPR):\n1. تأكد إن المكان آمن، وان الشخص مش بيتنفس أو مش بيستجيب.\n2. اطلب المساعدة فورًا (اتصل بالإسعاف).\n3. حط إيد على إيد في نص صدر الشخص.\n4. اضغط بقوة وسرعة (حوالي 100-120 ضغطة في الدقيقة) بعمق 5-6 سم.\n5. بعد كل 30 ضغطة، ممكن تعمل نفسين إنقاذ لو مدرب على كده، أو استمر في الضغطات بس.\n6. استمر لحد ما يوصل الإسعاف أو الشخص يستجيب.\n\n⚠️ ده شرح مبسط ومبيغنيش عن التدريب العملي على الـ CPR.",
    "مناورة هيمليخ": "👐 إزاي تعمل مناورة هيمليخ (لشخص واعي بيختنق):\n1. اقف ورا الشخص، وحاوط وسطه بإيديك.\n2. حط قبضة إيدك فوق سرة الشخص بشوية، وإيدك التانية فوقها.\n3. اضغط بقوة وسرعة للداخل وللأعلى في نفس الوقت، زي ما تكون عايز ترفع الشخص.\n4. كرر الضغطات دي لحد ما الجسم الغريب يطلع أو الشخص يفقد الوعي.\n5. لو فقد الوعي، مدد الشخص على الأرض وابتدي CPR.\n\n⚠️ ده شرح مبسط ومبيغنيش عن التدريب العملي على الإسعافات الأولية."
}

def handle_first_aid(text):
    """Check if the text contains first aid keywords and return appropriate guide"""
    text_lower = text.lower()
    for keyword, guide in first_aid_guides.items():
        if keyword in text_lower:
            return guide
    return None

def is_arabic(text):
    """Check if text contains Arabic characters"""
    for ch in text:
        if '\u0600' <= ch <= '\u06FF' or '\u0750' <= ch <= '\u077F':
            return True
    return False

def save_last_question(question):
    """Save the last question to memory file"""
    try:
        with open(MEMORY_FILE, "w", encoding="utf-8") as f:
            json.dump({"last_question": question}, f, ensure_ascii=False)
    except Exception as e:
        logger.error(f"Error saving last question: {e}")

def load_last_question():
    """Load the last question from memory file"""
    try:
        if os.path.exists(MEMORY_FILE):
            with open(MEMORY_FILE, "r", encoding="utf-8") as f:
                data = json.load(f)
                return data.get("last_question", "")
    except Exception as e:
        logger.error(f"Error loading last question: {e}")
    return ""

def log_conversation(question, answer):
    """Log conversation to history file"""
    try:
        data = []
        if os.path.exists(HISTORY_FILE):
            with open(HISTORY_FILE, "r", encoding="utf-8") as f:
                data = json.load(f)
        
        data.append({
            "q": question, 
            "a": answer,
            "timestamp": datetime.now().isoformat()
        })
        
        with open(HISTORY_FILE, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"Error logging conversation: {e}")

def is_emotional(text):
    """Check if text contains emotional triggers"""
    text_lower = text.lower()
    return any(trigger in text_lower for trigger in emotional_triggers)

def filter_medical_question(text):
    """Filter non-medical questions"""
    non_medical_keywords = ["سياسة", "رياضة", "فيلم", "موسيقى", "برمجة", "تعليم", "ترفيه"]
    text_lower = text.lower()
    if any(word in text_lower for word in non_medical_keywords):
        return False
    return True

def get_ai_response(user_input):
    """Get response from Google Gemini AI"""
    try:
        client = genai.Client(api_key=API_KEY)
        model = app.config['GOOGLE_MODEL']

        generate_content_config = types.GenerateContentConfig(
            safety_settings=[
                types.SafetySetting(category="HARM_CATEGORY_HARASSMENT", threshold="BLOCK_ONLY_HIGH"),
                types.SafetySetting(category="HARM_CATEGORY_HATE_SPEECH", threshold="BLOCK_ONLY_HIGH"),
                types.SafetySetting(category="HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold="BLOCK_ONLY_HIGH"),
                types.SafetySetting(category="HARM_CATEGORY_DANGEROUS_CONTENT", threshold="BLOCK_ONLY_HIGH"),
            ],
            response_mime_type="text/plain",
            system_instruction=[
                types.Part.from_text(text=app.config['SYSTEM_INSTRUCTION']),
            ],
        )

        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=user_input),
                ],
            ),
        ]

        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=generate_content_config,
        )

        return response.text

    except Exception as e:
        logger.error(f"Error getting AI response: {e}")
        return "عذراً، حدث خطأ في الحصول على الرد. يرجى المحاولة مرة أخرى."

@app.route('/')
def index():
    """Serve the main HTML page"""
    try:
        with open('index.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "HTML file not found", 404

@app.route('/favicon.ico')
def favicon():
    """Serve favicon"""
    return send_from_directory('.', 'favicon.svg', mimetype='image/svg+xml')

@app.route('/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('.', filename)

@app.route('/chat', methods=['POST'])
def chat():
    """Handle chat messages"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()

        if not user_message:
            return jsonify({'error': 'Empty message'}), 400

        # Check if it's a medical question
        if not filter_medical_question(user_message):
            response = "⚠️ عذرًا، السؤال مش طبي أو مش واضح، ممكن تسألني حاجة طبية؟"
            return jsonify({'response': response})

        # Check for emotional content
        if is_emotional(user_message):
            emotional_response = "🤗 باين عليك متوتر أو قلقان، خليك هادي وأنا هنا أساعدك.\n\n"
        else:
            emotional_response = ""

        # Check for first aid keywords
        first_aid_response = handle_first_aid(user_message)
        if first_aid_response:
            response = emotional_response + first_aid_response
        else:
            # Get AI response
            ai_response = get_ai_response(user_message)
            response = emotional_response + ai_response

        # Save conversation
        save_last_question(user_message)
        log_conversation(user_message, response)

        return jsonify({'response': response})

    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/history')
def get_history():
    """Get chat history"""
    try:
        if os.path.exists(HISTORY_FILE):
            with open(HISTORY_FILE, "r", encoding="utf-8") as f:
                data = json.load(f)
            return jsonify({'history': data})
        else:
            return jsonify({'history': []})
    except Exception as e:
        logger.error(f"Error getting history: {e}")
        return jsonify({'error': 'Error retrieving history'}), 500

@app.route('/clear-history', methods=['POST'])
def clear_history():
    """Clear chat history"""
    try:
        if os.path.exists(HISTORY_FILE):
            os.remove(HISTORY_FILE)
        if os.path.exists(MEMORY_FILE):
            os.remove(MEMORY_FILE)
        return jsonify({'success': True})
    except Exception as e:
        logger.error(f"Error clearing history: {e}")
        return jsonify({'error': 'Error clearing history'}), 500

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('static', exist_ok=True)
    
    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
