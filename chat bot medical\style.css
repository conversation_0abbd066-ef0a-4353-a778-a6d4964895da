/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #4CAF50;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.subtitle {
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 300;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Button Styles */
.btn-primary, .btn-secondary, .btn-send, .btn-attachment {
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.btn-secondary {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.btn-primary:hover, .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Main Content */
.main-content {
    padding: 20px 0;
    min-height: calc(100vh - 200px);
}

.chat-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    min-height: 600px;
    display: flex;
    flex-direction: column;
}

/* Welcome Message */
.welcome-message {
    padding: 40px;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.welcome-icon {
    font-size: 4rem;
    color: #4CAF50;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.welcome-content h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 10px;
}

.welcome-content p {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin-bottom: 30px;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 30px;
}

.quick-btn {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: 2px solid transparent;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    color: #495057;
}

.quick-btn:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.quick-btn i {
    font-size: 1.5rem;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 500px;
}

.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.message.bot .message-avatar {
    background: linear-gradient(45deg, #4CAF50, #45a049);
}

.message-content {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 20px;
    max-width: 70%;
    position: relative;
    line-height: 1.6;
}

.message.user .message-content {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.message-time {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    color: #6c757d;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4CAF50;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Chat Input */
.chat-input-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px 0;
}

.chat-input {
    background: white;
    border-radius: 25px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.input-group {
    display: flex;
    align-items: flex-end;
    padding: 15px;
    gap: 10px;
}

.btn-attachment {
    background: transparent;
    color: #6c757d;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-attachment:hover {
    background: #f8f9fa;
    color: #4CAF50;
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    line-height: 1.5;
    max-height: 120px;
    padding: 10px 15px;
    border-radius: 15px;
    background: #f8f9fa;
}

.btn-send {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-send:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

.btn-send:not(:disabled):hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.input-footer {
    padding: 10px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.disclaimer {
    color: #6c757d;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
}

.modal-header {
    padding: 25px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 15px;
}

.modal-header.emergency {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    flex: 1;
    margin: 0;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: inherit;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 25px;
}

.emergency-numbers {
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.emergency-number {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    font-weight: 600;
    color: #dc3545;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: #4CAF50;
}

.loading-spinner i {
    font-size: 3rem;
    animation: pulse 1.5s infinite;
    margin-bottom: 15px;
}

.loading-spinner p {
    font-size: 1.2rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .logo {
        flex-direction: column;
        gap: 5px;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .chat-input-container {
        padding: 15px 0;
    }
    
    .main-content {
        padding-bottom: 120px;
    }
}

@media (max-width: 480px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .logo h1 {
        font-size: 1.4rem;
    }
    
    .welcome-content h2 {
        font-size: 1.5rem;
    }
}
