/**
 * مساعد طبي ذكي - JavaScript Bootstrap
 * يتعامل مع الوظائف المشتركة عبر جميع الصفحات باستخدام Bootstrap 5
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة مكونات Bootstrap
    initializeBootstrapComponents();

    // التعامل مع نافذة تسجيل الدخول
    initializeLoginModal();

    // التعامل مع إرسال النماذج
    initializeFormHandlers();

    // التعامل مع التنقل
    initializeNavigation();

    // إصلاح مشاكل نظام حجز الأطباء
    fixAppointmentSystem();
});

/**
 * Initialize Bootstrap components
 */
function initializeBootstrapComponents() {
    // Initialize tooltips
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    
    // Initialize popovers
    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
    const popoverList = [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl));
}

/**
 * Initialize login modal functionality
 */
function initializeLoginModal() {
    const loginBtn = document.getElementById('loginBtn');
    const loginModal = document.getElementById('loginModal');
    
    if (loginBtn && loginModal) {
        const modal = new bootstrap.Modal(loginModal);
        
        loginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            modal.show();
        });
    }
}

/**
 * Initialize form handlers
 */
function initializeFormHandlers() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLoginSubmit);
    }
    
    // Register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegisterSubmit);
    }
    
    // Symptom form
    const symptomForm = document.getElementById('symptomForm');
    if (symptomForm) {
        symptomForm.addEventListener('submit', handleSymptomSubmit);
    }
    
    // Newsletter form
    const newsletterForms = document.querySelectorAll('form:has(input[type="email"][placeholder*="email"])');
    newsletterForms.forEach(form => {
        form.addEventListener('submit', handleNewsletterSubmit);
    });
}

/**
 * Handle login form submission
 */
function handleLoginSubmit(e) {
    e.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    
    // Basic validation
    if (!email || !password) {
        showAlert('Please fill in all fields', 'warning');
        return;
    }
    
    // Simulate login process
    showAlert('Login functionality will be implemented soon!', 'info');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    modal.hide();
}

/**
 * Handle register form submission
 */
function handleRegisterSubmit(e) {
    e.preventDefault();
    
    const name = document.getElementById('registerName').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    
    // Basic validation
    if (!name || !email || !password) {
        showAlert('Please fill in all fields', 'warning');
        return;
    }
    
    if (password.length < 6) {
        showAlert('Password must be at least 6 characters long', 'warning');
        return;
    }
    
    // Simulate registration process
    showAlert('Registration functionality will be implemented soon!', 'info');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    modal.hide();
}

/**
 * Handle symptom form submission
 */
function handleSymptomSubmit(e) {
    const symptoms = document.getElementById('symptoms').value.trim();
    
    if (!symptoms) {
        e.preventDefault();
        showAlert('Please describe your symptoms', 'warning');
        return;
    }
    
    if (symptoms.length < 10) {
        e.preventDefault();
        showAlert('Please provide more detailed symptoms (at least 10 characters)', 'warning');
        return;
    }
    
    // Show loading overlay if it exists
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('d-none');
        loadingOverlay.classList.add('d-flex');
    }
}

/**
 * Handle newsletter form submission
 */
function handleNewsletterSubmit(e) {
    e.preventDefault();
    
    const emailInput = e.target.querySelector('input[type="email"]');
    const email = emailInput.value.trim();
    
    if (!email) {
        showAlert('Please enter your email address', 'warning');
        return;
    }
    
    if (!isValidEmail(email)) {
        showAlert('Please enter a valid email address', 'warning');
        return;
    }
    
    // Simulate newsletter subscription
    showAlert('Thank you for subscribing to our newsletter!', 'success');
    emailInput.value = '';
}

/**
 * Initialize navigation functionality
 */
function initializeNavigation() {
    // Highlight current page in navigation
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'index.html')) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Utility function to format text
 */
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

/**
 * Handle print functionality
 */
function handlePrint() {
    window.print();
}

/**
 * إصلاح مشاكل نظام حجز الأطباء
 */
function fixAppointmentSystem() {
    // إصلاح مشكلة عدم تحميل بيانات الأطباء
    if (window.location.pathname.includes('doctors.html')) {
        fixDoctorsPage();
    }

    // إصلاح مشكلة عدم تحميل بيانات الحجز
    if (window.location.pathname.includes('booking.html')) {
        fixBookingPage();
    }

    // إصلاح مشكلة عدم عرض تأكيد الحجز
    if (window.location.pathname.includes('confirmation.html')) {
        fixConfirmationPage();
    }

    // إصلاح مشكلة عدم عرض المواعيد
    if (window.location.pathname.includes('my-appointments.html')) {
        fixMyAppointmentsPage();
    }
}

/**
 * إصلاح صفحة الأطباء
 */
function fixDoctorsPage() {
    // التأكد من تحميل بيانات الأطباء بشكل صحيح
    const doctorsGrid = document.getElementById('doctorsGrid');
    if (doctorsGrid && doctorsGrid.children.length === 0) {
        // إعادة تحميل بيانات الأطباء
        setTimeout(() => {
            if (typeof loadDoctors === 'function') {
                const urlParams = new URLSearchParams(window.location.search);
                const specialty = urlParams.get('specialty') || 'general';
                loadDoctors(specialty);
            }
        }, 100);
    }
}

/**
 * إصلاح صفحة الحجز
 */
function fixBookingPage() {
    // التأكد من تحميل بيانات الطبيب
    const doctorInfo = document.getElementById('doctorInfo');
    if (doctorInfo && doctorInfo.innerHTML.trim() === '') {
        setTimeout(() => {
            const urlParams = new URLSearchParams(window.location.search);
            const doctorId = urlParams.get('doctor');
            if (doctorId && typeof loadDoctorInfo === 'function') {
                // محاولة تحميل بيانات الطبيب من localStorage أو البيانات الافتراضية
                const defaultDoctor = {
                    name: 'د. طبيب متخصص',
                    image: 'AdobeStock_132944601_Preview.jpeg',
                    price: 300
                };
                loadDoctorInfo(defaultDoctor);
            }
        }, 100);
    }

    // إصلاح مشكلة عدم عمل التقويم
    fixCalendarIssues();
}

/**
 * إصلاح مشاكل التقويم
 */
function fixCalendarIssues() {
    const dateGrid = document.getElementById('dateGrid');
    if (dateGrid && dateGrid.children.length === 0) {
        setTimeout(() => {
            if (typeof generateDates === 'function') {
                generateDates();
            } else {
                // إنشاء تقويم بسيط
                createSimpleCalendar();
            }
        }, 100);
    }
}

/**
 * إنشاء تقويم بسيط
 */
function createSimpleCalendar() {
    const dateGrid = document.getElementById('dateGrid');
    if (!dateGrid) return;

    const today = new Date();
    const dates = [];

    for (let i = 0; i < 14; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        dates.push(date);
    }

    dateGrid.innerHTML = dates.map(date => {
        const dayName = date.toLocaleDateString('ar-EG', { weekday: 'short' });
        const dayNumber = date.getDate();
        const monthName = date.toLocaleDateString('ar-EG', { month: 'short' });
        const isWeekend = date.getDay() === 5 || date.getDay() === 6;

        return `
            <div class="col-6 col-md-4 col-lg-3">
                <div class="card calendar-day ${isWeekend ? 'unavailable' : ''}" data-date="${date.toISOString().split('T')[0]}">
                    <div class="card-body text-center p-2">
                        <div class="fw-bold">${dayName}</div>
                        <div class="h5 mb-0">${dayNumber}</div>
                        <div class="small text-muted">${monthName}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // إضافة مستمعي الأحداث
    addCalendarEventListeners();
}

/**
 * إضافة مستمعي أحداث التقويم
 */
function addCalendarEventListeners() {
    document.querySelectorAll('.calendar-day:not(.unavailable)').forEach(day => {
        day.addEventListener('click', function() {
            // إزالة التحديد السابق
            document.querySelectorAll('.calendar-day').forEach(d => d.classList.remove('selected'));

            // إضافة التحديد
            this.classList.add('selected');

            // تحديث المتغير العام
            if (window.selectedDate !== undefined) {
                window.selectedDate = this.dataset.date;
            }

            // إنشاء فترات زمنية
            generateTimeSlots();

            // إخفاء تنبيه عدم وجود أوقات
            const noTimesAlert = document.getElementById('noTimesAlert');
            if (noTimesAlert) {
                noTimesAlert.style.display = 'none';
            }
        });
    });
}

/**
 * إنشاء فترات زمنية
 */
function generateTimeSlots() {
    const timeSlotsContainer = document.getElementById('timeSlots');
    if (!timeSlotsContainer) return;

    const timeSlots = [
        '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
        '12:00', '12:30', '14:00', '14:30', '15:00', '15:30',
        '16:00', '16:30', '17:00', '17:30', '18:00', '18:30'
    ];

    const unavailableTimes = ['10:30', '14:30', '16:00'];

    timeSlotsContainer.innerHTML = timeSlots.map(time => {
        const isUnavailable = unavailableTimes.includes(time);
        return `
            <button class="btn btn-outline-primary time-slot ${isUnavailable ? 'unavailable' : ''}"
                    data-time="${time}" ${isUnavailable ? 'disabled' : ''}>
                ${time}
            </button>
        `;
    }).join('');

    // إضافة مستمعي الأحداث للأوقات
    addTimeSlotEventListeners();
}

/**
 * إضافة مستمعي أحداث الفترات الزمنية
 */
function addTimeSlotEventListeners() {
    document.querySelectorAll('.time-slot:not(.unavailable)').forEach(slot => {
        slot.addEventListener('click', function() {
            // إزالة التحديد السابق
            document.querySelectorAll('.time-slot').forEach(s => s.classList.remove('selected'));

            // إضافة التحديد
            this.classList.add('selected');

            // تحديث المتغير العام
            if (window.selectedTime !== undefined) {
                window.selectedTime = this.dataset.time;
            }

            // تحقق من اكتمال النموذج
            if (typeof checkFormCompletion === 'function') {
                checkFormCompletion();
            }
        });
    });
}

/**
 * إصلاح صفحة التأكيد
 */
function fixConfirmationPage() {
    // التحقق من وجود بيانات الحجز
    const bookingData = localStorage.getItem('bookingData');
    if (!bookingData) {
        // إنشاء بيانات تجريبية
        const sampleBookingData = {
            doctor: {
                name: 'د. طبيب متخصص',
                image: 'AdobeStock_132944601_Preview.jpeg',
                price: 300
            },
            date: new Date().toISOString().split('T')[0],
            time: '10:00',
            patient: {
                name: 'مريض تجريبي',
                phone: '01234567890',
                age: '30',
                gender: 'male'
            }
        };
        localStorage.setItem('bookingData', JSON.stringify(sampleBookingData));
    }
}

/**
 * إصلاح صفحة مواعيدي
 */
function fixMyAppointmentsPage() {
    const appointmentsList = document.getElementById('appointmentsList');
    if (appointmentsList && appointmentsList.children.length === 0) {
        // إضافة مواعيد تجريبية إذا لم توجد
        setTimeout(() => {
            if (typeof renderAppointments === 'function') {
                renderAppointments();
            }
        }, 100);
    }
}

// Export functions for use in other scripts
window.AImedical = {
    showAlert,
    isValidEmail,
    capitalizeFirstLetter,
    handlePrint,
    fixAppointmentSystem,
    createSimpleCalendar,
    generateTimeSlots
};
