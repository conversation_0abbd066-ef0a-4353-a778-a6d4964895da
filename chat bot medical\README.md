# المساعد الطبي الذكي - AI Medical Assistant

## 🩺 نظرة عامة

المساعد الطبي الذكي هو تطبيق ويب تفاعلي يستخدم الذكاء الاصطناعي لتقديم المساعدة الطبية والإسعافات الأولية باللغة العربية. يستخدم التطبيق Google Gemini AI لتقديم إجابات دقيقة ومفيدة للأسئلة الطبية.

## ✨ المميزات

- 🤖 **ذكاء اصطناعي متقدم**: يستخدم Google Gemini AI
- 🇸🇦 **دعم اللغة العربية**: واجهة وردود باللغة العربية
- 🚑 **إسعافات أولية**: دليل شامل للإسعافات الأولية
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة
- 💾 **حفظ المحادثات**: إمكانية حفظ وتصدير المحادثات
- ⚠️ **تنبيهات الطوارئ**: تنبيهات تلقائية للحالات الطارئة
- 🎨 **واجهة جميلة**: تصميم عصري وسهل الاستخدام

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية

- Python 3.8 أو أحدث
- pip (مدير حزم Python)
- مفتاح API من Google AI Studio

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd medical-chatbot
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
# على Windows
venv\Scripts\activate
# على macOS/Linux
source venv/bin/activate
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد مفتاح API**
- احصل على مفتاح API من [Google AI Studio](https://makersuite.google.com/app/apikey)
- افتح ملف `app.py`
- استبدل `API_KEY` بمفتاحك الخاص

5. **تشغيل التطبيق**
```bash
python app.py
```

6. **فتح التطبيق**
- افتح المتصفح واذهب إلى: `http://localhost:5000`

## 📁 هيكل المشروع

```
medical-chatbot/
├── index.html          # الصفحة الرئيسية
├── style.css           # ملف التصميم
├── script.js           # ملف JavaScript
├── app.py              # خادم Flask
├── requirements.txt    # متطلبات Python
├── README.md           # هذا الملف
├── memory.json         # ملف حفظ آخر سؤال
└── history.json        # ملف تاريخ المحادثات
```

## 🔧 الاستخدام

### الأسئلة المدعومة

- **الأعراض الطبية**: "عندي صداع شديد"
- **الإسعافات الأولية**: "إزاي أتعامل مع الحروق؟"
- **الإنعاش القلبي**: "إزاي أعمل CPR؟"
- **نتائج التحاليل**: "الهيموجلوبين 11.5"
- **النصائح الطبية**: "إزاي أحافظ على صحتي؟"

### الأزرار السريعة

- **إغماء**: للحصول على تعليمات التعامل مع الإغماء
- **حروق**: لمعرفة كيفية التعامل مع الحروق
- **إنعاش قلبي**: لتعلم الإنعاش القلبي الرئوي
- **صداع**: للحصول على نصائح لعلاج الصداع

### المميزات الإضافية

- **مسح المحادثة**: لحذف جميع الرسائل
- **تصدير المحادثة**: لحفظ المحادثة كملف نصي
- **تنبيهات الطوارئ**: تظهر تلقائياً عند ذكر كلمات الطوارئ

## ⚠️ تنبيهات مهمة

- هذا التطبيق **لا يغني عن استشارة الطبيب المختص**
- في حالات الطوارئ، اتصل بالإسعاف فوراً
- المعلومات المقدمة هي للإرشاد العام فقط

## 🛠️ التطوير

### إضافة ميزات جديدة

1. **إضافة إسعافات أولية جديدة**:
   - عدّل قاموس `first_aid_guides` في `app.py`

2. **تخصيص التصميم**:
   - عدّل ملف `style.css`

3. **إضافة وظائف JavaScript**:
   - عدّل ملف `script.js`

### المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إضافة التغييرات
4. إرسال Pull Request

## 📞 أرقام الطوارئ

- **الإسعاف**: 123
- **الطوارئ**: 16123
- **الشرطة**: 122
- **الإطفاء**: 180

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

## 🤝 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**تذكر**: هذا المساعد الطبي هو أداة مساعدة ولا يغني عن الاستشارة الطبية المتخصصة.
