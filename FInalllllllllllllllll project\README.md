# AI Medical Assistant

A modern, responsive web application that provides AI-powered medical symptom analysis and preliminary diagnosis.

## Features

- User-friendly interface for describing symptoms
- AI-powered analysis of symptoms
- Modern, responsive design for all screen sizes
- Interactive login/registration modal system
- Detailed results page with recommendations
- Enhanced user experience with animations and transitions

## Technologies Used

- HTML5
- CSS3 with Bootstrap 5
- JavaScript (ES6+)
- Bootstrap 5 framework
- PHP (backend API simulation)
- Font Awesome icons
- Google Fonts (Plus Jakarta Sans)

## Project Structure

- `index.html` - Main landing page
- `diagnosis.html` - Symptom input form
- `results.html` - Diagnosis results page
- `About.Html` - About the team page
- `js/` - JavaScript files
  - `main-bootstrap.js` - Core functionality with Bootstrap components
  - `diagnosis.js` - Diagnosis form handling
  - `results.js` - Results page functionality
- `style-bootstrap.css` - Custom styles on top of Bootstrap
- `process_diagnosis.php` - Backend API simulation (not functional in demo)

## Recent Redesign Improvements

The codebase has undergone a complete redesign with Bootstrap 5 to improve:

1. **Modern UI Design**
   - Implemented Bootstrap 5 for consistent components
   - Created a cleaner, more professional interface
   - Added subtle animations and transitions
   - Used custom Bootstrap theme with modern styling

2. **Enhanced User Experience**
   - Improved form interactions and feedback
   - Added interactive symptom selection buttons
   - Created step-based timeline for recommendations
   - Implemented toast notifications for user feedback

3. **Responsive Design**
   - Fully mobile-first approach
   - Optimized for all screen sizes and devices
   - Improved navigation on mobile with collapsible menu
   - Better layout handling for small screens

4. **Code Organization**
   - Modular JavaScript with clear functions
   - Consistent component styling
   - Better class naming following Bootstrap conventions
   - Improved structure for maintainability

5. **Accessibility**
   - Improved ARIA attributes for interactive elements
   - Better color contrast for readability
   - Focus states for keyboard navigation
   - Screen reader friendly markup

## Setup Instructions

### Development Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-medical-assistant
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:5173`

### Simple Setup

1. Clone the repository
2. Open `index.html` in your browser
3. For full functionality including the backend API, you'll need a PHP server

### Build for Production

```bash
npm run build
```

## Browser Compatibility

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome for Android)

## Recent Fixes and Improvements (Version 2.0)

### ✅ Design Unification
- **Unified Bootstrap 5**: Replaced multiple CSS frameworks with consistent Bootstrap 5 implementation
- **Consistent Color Scheme**: Applied medical blue theme across all pages
- **Typography Standardization**: Used Plus Jakarta Sans font family throughout
- **Component Consistency**: Standardized buttons, cards, forms, and navigation

### ✅ JavaScript Improvements
- **Modular Architecture**: Created `js/main-bootstrap.js` with organized functions
- **Form Validation**: Added comprehensive client-side validation
- **Modal Functionality**: Fixed login/register modal interactions
- **Error Handling**: Implemented user-friendly error messages and alerts

### ✅ File Structure Cleanup
- **Removed Duplicates**: Eliminated multiple index files (index2.html through index13.html)
- **Fixed Navigation**: Updated all links to point to correct pages
- **Created Contact Page**: Replaced "index face.html" with proper contact.html
- **Organized Assets**: Properly structured JavaScript and CSS files

### ✅ Responsive Design Fixes
- **Mobile Optimization**: Fixed responsive issues on all screen sizes
- **Navigation**: Improved mobile menu functionality
- **Layout Consistency**: Ensured consistent spacing and alignment
- **Touch Interactions**: Enhanced mobile touch targets

### ✅ Accessibility Improvements
- **ARIA Labels**: Added proper accessibility attributes
- **Keyboard Navigation**: Improved focus states and tab order
- **Color Contrast**: Enhanced readability with better contrast ratios
- **Screen Reader Support**: Added semantic HTML structure

### ✅ Performance Optimizations
- **CSS Optimization**: Consolidated stylesheets and removed unused code
- **JavaScript Efficiency**: Optimized event handlers and DOM manipulation
- **Image Optimization**: Properly sized and compressed images
- **Loading States**: Added smooth loading animations

## Future Improvements

- Implement actual backend functionality with real AI integration
- Add user accounts and data persistence
- Enhance the AI analysis with more detailed medical results
- Add multilingual support (Arabic, Spanish, French)
- Implement dark mode toggle
- Create a progressive web app (PWA) version
- Add offline functionality
- Implement real-time chat support
- Add medical history tracking
- Create mobile app versions (iOS/Android)

## Team Members

- **Dr. John Smith** - Project Supervisor & Medical Advisor
- **Mona Allah Hamada Al-Sheikh** - Frontend Developer
- **Ahmed Gamal Ramadan** - Backend Developer
- **Khaled Mahmoud Al-Adrous Abu Al-Anin** - AI Specialist
- **Omar Maher Mahmoud Al-Saeed Al-Alfy** - Data Analyst
- **Ahmed Nader Ahmed Elbialy** - Database Administrator
- **Ahmed Hani Al-Najjar** - System Architect
- **Omar Ahmed Fathi** - DevOps Engineer

## Contact & Support

- **Email**: <EMAIL>
- **Phone**: +****************
- **Address**: 123 Health Street, Medical City
- **Hours**: Mon-Fri 9AM-6PM, Sat-Sun 10AM-4PM

---

**Made with ❤️ for better healthcare** | **Version 2.0** | **Last Updated: 2024**