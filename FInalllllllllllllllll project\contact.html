<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا - مساعد طبي ذكي</title>
    <meta name="description" content="تواصل مع فريق المساعد الطبي الذكي للحصول على الدعم والاستفسارات">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style-bootstrap.css">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <i class="fas fa-heartbeat text-primary me-2"></i>
                <span class="fw-bold">مساعد طبي ذكي</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="diagnosis.html">التشخيص</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.html">حجز موعد</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.html">اتصل بنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="About.Html">عن المشروع</a>
                    </li>
                    <li class="nav-item ms-lg-3 mt-2 mt-lg-0">
                        <button class="btn btn-primary rounded-pill px-4" id="loginBtn">تسجيل الدخول</button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content rounded-4 shadow">
                <div class="modal-header p-4 pb-0 border-bottom-0">
                    <ul class="nav nav-tabs nav-fill w-100" id="authTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#login-tab-pane" type="button" role="tab" aria-controls="login-tab-pane" aria-selected="true">Login</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register-tab-pane" type="button" role="tab" aria-controls="register-tab-pane" aria-selected="false">Register</button>
                        </li>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4 pt-0">
                    <div class="tab-content" id="authTabContent">
                        <!-- Login Form -->
                        <div class="tab-pane fade show active" id="login-tab-pane" role="tabpanel" aria-labelledby="login-tab" tabindex="0">
                            <form id="loginForm" class="mt-4">
                                <div class="mb-3">
                                    <label for="loginEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="loginEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="loginPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="loginPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mt-3 rounded-pill">Login</button>
                            </form>
                        </div>
                        <!-- Register Form -->
                        <div class="tab-pane fade" id="register-tab-pane" role="tabpanel" aria-labelledby="register-tab" tabindex="0">
                            <form id="registerForm" class="mt-4">
                                <div class="mb-3">
                                    <label for="registerName" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="registerName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="registerEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="registerPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mt-3 rounded-pill">Register</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="py-5 py-md-6 mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <!-- Header -->
                    <div class="text-center mb-5">
                        <h1 class="display-4 fw-bold text-gradient mb-3">Contact Us</h1>
                        <p class="lead text-muted">We're here to help you with any questions about our AI Medical Assistant</p>
                    </div>

                    <div class="row g-5">
                        <!-- Contact Form -->
                        <div class="col-lg-8">
                            <div class="bg-white shadow-lg rounded-4 p-4 p-md-5">
                                <h2 class="h3 fw-bold mb-4">Send us a Message</h2>
                                <form id="contactForm">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="firstName" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="firstName" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="lastName" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="lastName" required>
                                        </div>
                                        <div class="col-12">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="email" required>
                                        </div>
                                        <div class="col-12">
                                            <label for="subject" class="form-label">Subject</label>
                                            <select class="form-select" id="subject" required>
                                                <option value="">Choose a subject...</option>
                                                <option value="general">General Inquiry</option>
                                                <option value="technical">Technical Support</option>
                                                <option value="feedback">Feedback</option>
                                                <option value="partnership">Partnership</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                        <div class="col-12">
                                            <label for="message" class="form-label">Message</label>
                                            <textarea class="form-control" id="message" rows="5" placeholder="Tell us how we can help you..." required></textarea>
                                        </div>
                                        <div class="col-12">
                                            <button type="submit" class="btn btn-primary btn-lg px-5">
                                                <i class="fas fa-paper-plane me-2"></i>
                                                Send Message
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="col-lg-4">
                            <div class="bg-white shadow-lg rounded-4 p-4 p-md-5 h-100">
                                <h3 class="h4 fw-bold mb-4">Get in Touch</h3>
                                
                                <div class="contact-item mb-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="contact-icon bg-primary text-white rounded-circle me-3">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <h5 class="mb-0">Email</h5>
                                    </div>
                                    <p class="text-muted mb-0 ms-5"><EMAIL></p>
                                </div>

                                <div class="contact-item mb-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="contact-icon bg-primary text-white rounded-circle me-3">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <h5 class="mb-0">Phone</h5>
                                    </div>
                                    <p class="text-muted mb-0 ms-5">+****************</p>
                                </div>

                                <div class="contact-item mb-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="contact-icon bg-primary text-white rounded-circle me-3">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <h5 class="mb-0">Address</h5>
                                    </div>
                                    <p class="text-muted mb-0 ms-5">123 Health Street<br>Medical City, MC 12345</p>
                                </div>

                                <div class="contact-item">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="contact-icon bg-primary text-white rounded-circle me-3">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <h5 class="mb-0">Hours</h5>
                                    </div>
                                    <p class="text-muted mb-0 ms-5">
                                        Mon - Fri: 9:00 AM - 6:00 PM<br>
                                        Sat - Sun: 10:00 AM - 4:00 PM
                                    </p>
                                </div>

                                <hr class="my-4">

                                <div class="text-center">
                                    <h5 class="fw-bold mb-3">Follow Us</h5>
                                    <div class="d-flex justify-content-center gap-3">
                                        <a href="#" class="btn btn-outline-primary btn-sm rounded-circle">
                                            <i class="fab fa-facebook-f"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-primary btn-sm rounded-circle">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-primary btn-sm rounded-circle">
                                            <i class="fab fa-linkedin-in"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-primary btn-sm rounded-circle">
                                            <i class="fab fa-instagram"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container py-4">
            <div class="row g-4">
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="About.Html" class="link-light text-decoration-none">About Us</a></li>
                        <li class="mb-2"><a href="diagnosis.html" class="link-light text-decoration-none">Get Diagnosis</a></li>
                        <li class="mb-2"><a href="privacy.html" class="link-light text-decoration-none">Privacy Policy</a></li>
                        <li class="mb-2"><a href="terms.html" class="link-light text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Connect With Us</h5>
                    <div class="d-flex gap-3">
                        <a href="#" class="link-light fs-5" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Contact</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i>+****************</li>
                        <li class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>123 Health Street, Medical City</li>
                    </ul>
                </div>
                <div class="col-md-12 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Subscribe to Our Newsletter</h5>
                    <form class="d-flex">
                        <input type="email" class="form-control" placeholder="Your email">
                        <button type="submit" class="btn btn-primary ms-2">Subscribe</button>
                    </form>
                </div>
            </div>
            <hr class="my-4 border-light">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; 2024 AI Medical Assistant. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end mt-3 mt-md-0">
                    <small>Made with <i class="fas fa-heart text-danger"></i> for better healthcare</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Arabic Localization -->
    <script src="js/arabic-localization.js"></script>
    <!-- Custom JS -->
    <script src="js/main-bootstrap.js"></script>
    
    <style>
    .contact-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }
    </style>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const contactForm = document.getElementById('contactForm');
        
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form data
                const firstName = document.getElementById('firstName').value;
                const lastName = document.getElementById('lastName').value;
                const email = document.getElementById('email').value;
                const subject = document.getElementById('subject').value;
                const message = document.getElementById('message').value;
                
                // Basic validation
                if (!firstName || !lastName || !email || !subject || !message) {
                    AImedical.showAlert('Please fill in all fields', 'warning');
                    return;
                }
                
                // Simulate form submission
                AImedical.showAlert('Thank you for your message! We\'ll get back to you soon.', 'success');
                
                // Reset form
                contactForm.reset();
            });
        }
    });
    </script>
</body>
</html>
