/**
 * AI Medical Assistant - Modern CSS
 * Using BEM naming convention and CSS custom properties
 */

/* ========== RESET & BASE STYLES ========== */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color system */
    --color-primary: #2563eb;        /* Brighter blue for better visibility */
    --color-primary-light: #60a5fa;
    --color-primary-dark: #1e40af;
    --color-secondary: #f97316;      /* Orange for action items */
    --color-secondary-light: #fdba74;
    --color-text: #1e293b;           /* Slate for better readability */
    --color-text-light: #64748b;
    --color-background: #f8fafc;
    --color-white: #ffffff;
    --color-gray-100: #f1f5f9;
    --color-gray-200: #e2e8f0;
    --color-gray-300: #cbd5e1;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-danger: #dc2626;
    
    /* Typography */
    --font-family-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 
                        'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 
                        'Open Sans', 'Helvetica Neue', sans-serif;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Spacing system (rem based for scaling) */
    --space-1: 0.25rem;  /* 4px */
    --space-2: 0.5rem;   /* 8px */
    --space-3: 0.75rem;  /* 12px */
    --space-4: 1rem;     /* 16px */
    --space-6: 1.5rem;   /* 24px */
    --space-8: 2rem;     /* 32px */
    --space-12: 3rem;    /* 48px */
    --space-16: 4rem;    /* 64px */
    
    /* Border radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 
                 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Container max widths */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    
    /* Z-index layers */
    --z-navigation: 1000;
    --z-dropdown: 1010;
    --z-modal: 2000;
}

html, body {
    height: 100%;
    font-family: var(--font-family-sans);
    font-size: 16px;
    line-height: 1.5;
    color: var(--color-text);
    background-color: var(--color-background);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

main {
    flex-grow: 1;
}

img {
    max-width: 100%;
    height: auto;
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-dark);
}

a:focus {
    outline: 2px solid var(--color-primary-light);
    outline-offset: 2px;
}

button, input, select, textarea {
    font: inherit;
}

/* ========== LAYOUT ========== */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--space-4);
    padding-right: var(--space-4);
}

.section {
    padding-top: var(--space-12);
    padding-bottom: var(--space-12);
}

.section--tight {
    padding-top: var(--space-8);
    padding-bottom: var(--space-8);
}

/* ========== TYPOGRAPHY ========== */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--space-4);
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    color: var(--color-text);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

h5 {
    font-size: 1.125rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: var(--space-4);
}

.text-center {
    text-align: center;
}

.text-sm {
    font-size: 0.875rem;
}

.text-lg {
    font-size: 1.125rem;
}

.text-light {
    color: var(--color-text-light);
}

.text-primary {
    color: var(--color-primary);
}

/* ========== COMPONENTS ========== */

/* Header & Navigation */
.header {
    background-color: var(--color-white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-navigation);
}

.header__container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
}

.header__logo {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: var(--font-weight-bold);
    font-size: 1.25rem;
    color: var(--color-primary);
}

.header__logo-icon {
    color: var(--color-primary);
}

.nav {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.nav__list {
    display: flex;
    gap: var(--space-4);
    list-style: none;
}

.nav__item {
    position: relative;
}

.nav__link {
    display: block;
    padding: var(--space-2) var(--space-3);
    color: var(--color-text);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.nav__link:hover {
    color: var(--color-primary);
    background-color: var(--color-gray-100);
}

.nav__link--active {
    color: var(--color-primary);
    background-color: var(--color-gray-100);
}

.nav__toggle {
    display: none;
    background: transparent;
    border: none;
    color: var(--color-text);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--space-2);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
    text-decoration: none;
    white-space: nowrap;
    gap: var(--space-2);
}

.btn--primary {
    background-color: var(--color-primary);
    color: var(--color-white);
}

.btn--primary:hover {
    background-color: var(--color-primary-dark);
    color: var(--color-white);
}

.btn--secondary {
    background-color: var(--color-secondary);
    color: var(--color-white);
}

.btn--secondary:hover {
    background-color: var(--color-secondary-light);
    color: var(--color-white);
}

.btn--outline {
    background-color: transparent;
    color: var(--color-primary);
    border: 1px solid var(--color-primary);
}

.btn--outline:hover {
    background-color: var(--color-primary);
    color: var(--color-white);
}

.btn--gray {
    background-color: var(--color-gray-200);
    color: var(--color-text);
}

.btn--gray:hover {
    background-color: var(--color-gray-300);
}

.btn--sm {
    padding: var(--space-1) var(--space-3);
    font-size: 0.875rem;
}

.btn--lg {
    padding: var(--space-4) var(--space-8);
    font-size: 1.125rem;
}

.btn--full {
    width: 100%;
}

/* Cards */
.card {
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card__body {
    padding: var(--space-6);
}

.card__title {
    margin-bottom: var(--space-3);
    font-size: 1.25rem;
    font-weight: var(--font-weight-semibold);
}

.card__text {
    color: var(--color-text-light);
    margin-bottom: var(--space-4);
}

.card__icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-full);
    background-color: var(--color-primary-light);
    color: var(--color-primary-dark);
    font-size: 1.5rem;
    margin-bottom: var(--space-4);
}

/* Form Elements */
.form {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.form__group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form__label {
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
}

.form__input,
.form__textarea,
.form__select {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid var(--color-gray-300);
    border-radius: var(--radius-md);
    background-color: var(--color-white);
    color: var(--color-text);
    transition: border-color var(--transition-normal);
}

.form__input:focus,
.form__textarea:focus,
.form__select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form__textarea {
    min-height: 150px;
    resize: vertical;
}

.form__row {
    display: flex;
    gap: var(--space-4);
}

.form__actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-4);
}

/* Hero Section */
.hero {
    position: relative;
    padding: var(--space-16) 0;
    background-image: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), 
                      url('background.jpg');
    background-size: cover;
    background-position: center;
    color: var(--color-white);
    text-align: center;
}

.hero__content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.hero__title {
    font-size: 3rem;
    margin-bottom: var(--space-4);
    color: var(--color-white);
}

.hero__subtitle {
    font-size: 1.25rem;
    margin-bottom: var(--space-8);
    color: var(--color-gray-200);
}

.hero__actions {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

/* Features */
.features {
    background-color: var(--color-white);
}

.features__header {
    max-width: 700px;
    margin: 0 auto var(--space-12);
    text-align: center;
}

.features__title {
    color: var(--color-text);
    margin-bottom: var(--space-4);
}

.features__subtitle {
    color: var(--color-text-light);
}

.features__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
}

/* Steps */
.steps {
    background-color: var(--color-gray-100);
}

.steps__header {
    max-width: 700px;
    margin: 0 auto var(--space-12);
    text-align: center;
}

.steps__title {
    color: var(--color-text);
    margin-bottom: var(--space-4);
}

.steps__subtitle {
    color: var(--color-text-light);
}

.steps__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
}

.step-card {
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--space-6);
    position: relative;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    background-color: var(--color-primary);
    color: var(--color-white);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-bold);
    font-size: 1.25rem;
    margin-bottom: var(--space-4);
}

/* Diagnosis Form */
.diagnosis-page {
    padding: var(--space-12) var(--space-4);
    max-width: var(--container-lg);
    margin: 0 auto;
}

.diagnosis-form {
    background-color: var(--color-white);
    padding: var(--space-8);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--space-8);
}

.tips-card {
    background-color: var(--color-gray-100);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    border-left: 4px solid var(--color-secondary);
}

.tips-title {
    color: var(--color-primary);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-2);
}

.tips-list {
    padding-left: var(--space-6);
}

/* Results Page */
.results-page {
    padding: var(--space-12) var(--space-4);
    max-width: var(--container-lg);
    margin: 0 auto;
}

.results-container {
    background-color: var(--color-white);
    padding: var(--space-8);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.disclaimer {
    background-color: #fef2f2;
    color: #991b1b;
    padding: var(--space-4);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-6);
    border-left: 4px solid var(--color-danger);
}

.condition {
    background-color: var(--color-gray-100);
    border-radius: var(--radius-md);
    padding: var(--space-4);
    margin-bottom: var(--space-4);
    border: 1px solid var(--color-gray-200);
}

.condition__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
}

.condition__name {
    font-weight: var(--font-weight-semibold);
    font-size: 1.25rem;
}

.condition__probability {
    display: inline-block;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: var(--font-weight-medium);
    color: var(--color-white);
}

.condition__probability--high {
    background-color: var(--color-danger);
}

.condition__probability--medium {
    background-color: var(--color-warning);
}

.condition__probability--low {
    background-color: var(--color-primary);
}

.condition__description {
    color: var(--color-text-light);
    margin-bottom: var(--space-3);
}

.recommendations__title {
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-2);
}

.recommendations__list {
    list-style-type: none;
}

.recommendations__item {
    padding: var(--space-2) 0 var(--space-2) var(--space-6);
    position: relative;
}

.recommendations__item::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--color-success);
    font-weight: var(--font-weight-bold);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: var(--z-modal);
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
}

.modal.active {
    display: flex;
}

.modal__content {
    width: 100%;
    max-width: 450px;
    background-color: var(--color-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--space-6);
    position: relative;
}

.modal__close {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--color-text-light);
    transition: color var(--transition-fast);
}

.modal__close:hover {
    color: var(--color-text);
}

.modal__tabs {
    display: flex;
    border-bottom: 2px solid var(--color-gray-200);
    margin-bottom: var(--space-6);
}

.modal__tab {
    padding: var(--space-3) var(--space-4);
    cursor: pointer;
    color: var(--color-text-light);
    font-weight: var(--font-weight-medium);
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
}

.modal__tab--active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
}

/* Loading Overlay */
.loading-overlay {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: var(--z-modal);
}

.loading-overlay.hidden {
    display: none;
}

.loader {
    width: 3rem;
    height: 3rem;
    border: 4px solid var(--color-gray-200);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--space-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Footer */
.footer {
    background-color: var(--color-text);
    color: var(--color-gray-300);
    padding: var(--space-8) 0 var(--space-4);
    margin-top: auto;
}

.footer__container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-8);
}

.footer__section {
    margin-bottom: var(--space-4);
}

.footer__title {
    color: var(--color-white);
    font-size: 1.125rem;
    margin-bottom: var(--space-4);
}

.footer__list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.footer__link {
    color: var(--color-gray-300);
    transition: color var(--transition-fast);
}

.footer__link:hover {
    color: var(--color-white);
}

.footer__social {
    display: flex;
    gap: var(--space-4);
    margin-top: var(--space-2);
}

.footer__social-link {
    color: var(--color-gray-300);
    font-size: 1.25rem;
    transition: color var(--transition-fast);
}

.footer__social-link:hover {
    color: var(--color-white);
}

.footer__bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: var(--space-6);
    padding-top: var(--space-6);
    text-align: center;
}

/* ========== UTILITIES ========== */

/* Spacing utilities */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-8 { margin-bottom: var(--space-8); }

.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }

/* Display utilities */
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-4 { gap: var(--space-4); }

/* ========== RESPONSIVE STYLES ========== */
@media (max-width: 1024px) {
    :root {
        --space-12: 2.5rem;
        --space-16: 3rem;
    }
    
    h1 {
        font-size: 2.25rem;
    }
    
    .hero__title {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    :root {
        --space-12: 2rem;
        --space-16: 2.5rem;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    .header__container {
        flex-wrap: wrap;
    }
    
    .nav {
        width: 100%;
        justify-content: flex-end;
    }
    
    .nav__toggle {
        display: block;
    }
    
    .nav__list {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        flex-direction: column;
        background-color: var(--color-white);
        box-shadow: var(--shadow-md);
        padding: var(--space-2) 0;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        z-index: -1;
    }
    
    .nav__list.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
        z-index: 1;
    }
    
    .nav__item {
        width: 100%;
    }
    
    .nav__link {
        padding: var(--space-3) var(--space-4);
        width: 100%;
    }
    
    .hero__title {
        font-size: 2rem;
    }
    
    .hero__subtitle {
        font-size: 1.125rem;
    }
    
    .form__row {
        flex-direction: column;
        gap: var(--space-3);
    }
    
    .footer__container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    :root {
        --space-8: 1.5rem;
    }
    
    h1 {
        font-size: 1.75rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .hero__title {
        font-size: 1.75rem;
    }
    
    .hero__actions {
        flex-direction: column;
        gap: var(--space-3);
    }
    
    .btn--lg {
        padding: var(--space-3) var(--space-6);
        font-size: 1rem;
    }
    
    .form__actions {
        flex-direction: column;
        gap: var(--space-3);
    }
    
    .form__actions .btn {
        width: 100%;
    }
}
