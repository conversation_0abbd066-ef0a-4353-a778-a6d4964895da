:root {
    --primary-color: #1f509a;
    --secondary-color: #0a3981;
    --accent-color: #e38e49;
    --text-color: #0a3981;
    --light-gray: #d4ebf8;
    --white: #ffffff;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Inter', sans-serif, Arial;
    background-image: url('background.jpg'); 
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    color: #333;
    margin: 0;
    padding: 20px;
}

.navbar {
    background: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-links {
    display: flex;
    gap: 2rem;
    list-style: none;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.nav-links li {
    margin: 0;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    display: inline-block;
}

.nav-links a:hover {
    color: var(--primary-color);
    background: var(--light-gray);
}

.nav-links a.active {
    color: var(--primary-color);
    background: var(--light-gray);
}

.login-btn {
    background: var(--primary-color);
    color: var(--white) !important;
    padding: 0.5rem 1.5rem !important;
    border-radius: 0.375rem;
    transition: all 0.3s ease !important;
    cursor: pointer;
    text-align: center;
    display: inline-block;
}

.login-btn:hover {
    background: var(--secondary-color) !important;
    color: var(--white) !important;
}

.container {
    max-width: 800px;
    margin: 100px auto 40px; /* margin-top to avoid navbar overlap */
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h1, h2 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.section {
    margin-top: 30px;
}

.profile-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    justify-content: center;
}

.profile-item {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 10px;
    box-shadow: 1px 1px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.profile-item:hover {
    transform: translateY(-5px);
    box-shadow: 1px 5px 15px rgba(0,0,0,0.2);
}

.profile-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
    border: 2px solid #ccc;
    flex-shrink: 0;
}

/* Diagnosis page specific styles */
.diagnosis-page {
    padding-top: 80px;
    max-width: 800px;
    margin: 0 auto;
}

.diagnosis-form {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.diagnosis-form h1 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 1rem;
}

.description {
    text-align: center;
    color: #555;
    margin-bottom: 2rem;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.back-btn {
    background: #f1f5f9;
    color: var(--text-color);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
}

.back-btn:hover {
    background: #e2e8f0;
}

.submit-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s;
}

.submit-btn:hover {
    background: var(--secondary-color);
}

.tips {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 8px;
    border-left: 4px solid var(--accent-color);
    margin-top: 30px;
}

.tips h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.tips ul {
    padding-left: 20px;
    margin: 0;
}

.tips li {
    margin-bottom: 6px;
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: all 0.3s;
}

.hidden {
    display: none;
}

.loader {
    border: 5px solid var(--light-gray);
    border-radius: 50%;
    border-top: 5px solid var(--primary-color);
    width: 50px;
    height: 50px;
    animation: spin 2s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

footer {
    margin-top: 30px;
    font-style: italic;
    text-align: center;
    color: #555;
}

/* Responsive styles */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .profile-list {
        grid-template-columns: 1fr;
    }
    
    .nav-links {
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
    }
    
    .diagnosis-form {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .diagnosis-form h1 {
        font-size: 1.5rem;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .back-btn, .submit-btn {
        width: 100%;
        justify-content: center;
    }
}

.project-description {
    text-align: center;
    color: #555;
    margin-bottom: 30px;
    font-size: 1.1rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}