<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المساعد الطبي الذكي - AI Medical Assistant</title>
    <meta name="description" content="مساعد طبي ذكي يستخدم الذكاء الاصطناعي لتقديم المساعدة الطبية والإسعافات الأولية باللغة العربية">
    <meta name="keywords" content="طبي, صحة, إسعافات أولية, ذكاء اصطناعي, مساعد طبي">
    <meta name="author" content="Medical AI Assistant">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#4CAF50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="مساعد طبي">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-user-md"></i>
                    <h1>المساعد الطبي الذكي</h1>
                    <span class="subtitle">AI Medical Assistant</span>
                </div>
                <div class="header-actions">
                    <button class="btn-secondary" id="clearChat">
                        <i class="fas fa-trash"></i>
                        مسح المحادثة
                    </button>
                    <button class="btn-secondary" id="exportChat">
                        <i class="fas fa-download"></i>
                        تصدير المحادثة
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="chat-container">
                <!-- Welcome Message -->
                <div class="welcome-message" id="welcomeMessage">
                    <div class="welcome-content">
                        <i class="fas fa-stethoscope welcome-icon"></i>
                        <h2>مرحباً بك في المساعد الطبي الذكي</h2>
                        <p>أنا هنا لمساعدتك في الأسئلة الطبية والإسعافات الأولية</p>
                        <div class="quick-actions">
                            <button class="quick-btn" data-message="أعمل إيه لو حد اغمى عليه؟">
                                <i class="fas fa-dizzy"></i>
                                إغماء
                            </button>
                            <button class="quick-btn" data-message="إزاي أتعامل مع الحروق؟">
                                <i class="fas fa-fire"></i>
                                حروق
                            </button>
                            <button class="quick-btn" data-message="إزاي أعمل CPR؟">
                                <i class="fas fa-heartbeat"></i>
                                إنعاش قلبي
                            </button>
                            <button class="quick-btn" data-message="عندي صداع شديد">
                                <i class="fas fa-head-side-virus"></i>
                                صداع
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    <!-- Messages will be added here dynamically -->
                </div>

                <!-- Typing Indicator -->
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span>المساعد الطبي يكتب...</span>
                </div>
            </div>
        </div>
    </main>

    <!-- Chat Input -->
    <div class="chat-input-container">
        <div class="container">
            <div class="chat-input">
                <div class="input-group">
                    <button class="btn-attachment" id="attachBtn" title="إرفاق ملف">
                        <i class="fas fa-paperclip"></i>
                    </button>
                    <textarea 
                        id="messageInput" 
                        placeholder="اكتب سؤالك الطبي هنا... (مثال: عندي صداع شديد)"
                        rows="1"
                    ></textarea>
                    <button class="btn-send" id="sendBtn" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="input-footer">
                    <small class="disclaimer">
                        <i class="fas fa-exclamation-triangle"></i>
                        هذا المساعد لا يغني عن استشارة الطبيب المختص
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Modal -->
    <div class="modal" id="emergencyModal">
        <div class="modal-content">
            <div class="modal-header emergency">
                <i class="fas fa-ambulance"></i>
                <h3>حالة طوارئ؟</h3>
                <button class="modal-close" id="closeEmergency">&times;</button>
            </div>
            <div class="modal-body">
                <p>إذا كانت هذه حالة طوارئ طبية، يرجى الاتصال فوراً بـ:</p>
                <div class="emergency-numbers">
                    <div class="emergency-number">
                        <i class="fas fa-phone"></i>
                        <span>الإسعاف: 123</span>
                    </div>
                    <div class="emergency-number">
                        <i class="fas fa-hospital"></i>
                        <span>الطوارئ: 16123</span>
                    </div>
                </div>
                <button class="btn-primary" id="continueChat">متابعة المحادثة</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-heartbeat"></i>
            <p>جاري التحليل...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
