# المساعد الطبي الذكي - Medical Chatbot

## نظرة عامة
تم دمج الشات بوت الطبي الذكي في صفحة التشخيص الخاصة بالمشروع النهائي. يستخدم الشات بوت تقنية الذكاء الاصطناعي من Google Gemini لتقديم المساعدة الطبية والإسعافات الأولية باللغة العربية.

## المميزات
- 🤖 مساعد طبي ذكي يعمل بالذكاء الاصطناعي
- 🩺 إرشادات الإسعافات الأولية
- 🚨 كشف حالات الطوارئ تلقائياً
- 💾 حفظ تاريخ المحادثات
- 📱 تصميم متجاوب يعمل على جميع الأجهزة
- 🔒 آمن ومحمي

## كيفية التشغيل

### الطريقة الأولى: استخدام ملف Batch (الأسهل)
1. تأكد من تثبيت Python 3.8 أو أحدث
2. انقر نقراً مزدوجاً على ملف `run_chatbot.bat`
3. انتظر حتى يتم تثبيت المتطلبات وتشغيل الخادم
4. افتح المتصفح واذهب إلى: http://localhost:5000

### الطريقة الثانية: التشغيل اليدوي
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد المشروع:
   ```
   cd "FInalllllllllllllllll project"
   ```
3. ثبت المتطلبات:
   ```
   pip install -r requirements.txt
   ```
4. شغل التطبيق:
   ```
   python app.py
   ```
5. افتح المتصفح واذهب إلى: http://localhost:5000

## الملفات المهمة
- `diagnosis.html` - الصفحة الرئيسية للشات بوت
- `app.py` - خادم Flask الرئيسي
- `config.py` - إعدادات التطبيق
- `requirements.txt` - المكتبات المطلوبة
- `run_chatbot.bat` - ملف تشغيل سريع

## إعداد Google API Key
لتفعيل الذكاء الاصطناعي، تحتاج إلى:
1. الحصول على API Key من Google AI Studio
2. إضافة المفتاح في ملف `config.py`
3. أو تعيينه كمتغير بيئة: `GOOGLE_API_KEY`

## الاستخدام
1. اكتب سؤالك الطبي في مربع النص
2. اضغط Enter أو انقر على زر الإرسال
3. انتظر رد المساعد الطبي
4. يمكنك استخدام الأزرار السريعة للأسئلة الشائعة

## تحذير مهم
⚠️ هذا المساعد لا يغني عن استشارة الطبيب المختص. في حالات الطوارئ، اتصل بالإسعاف فوراً.

## الدعم الفني
في حالة وجود مشاكل:
1. تأكد من تثبيت Python بشكل صحيح
2. تأكد من الاتصال بالإنترنت
3. تحقق من صحة Google API Key
4. راجع رسائل الخطأ في Command Prompt

## رقم الطوارئ
- الإسعاف: 123
- الطوارئ: 16123
