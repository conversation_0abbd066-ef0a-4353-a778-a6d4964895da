# دليل التشغيل السريع - المسا<PERSON>د الطبي الذكي

## 🚀 التشغيل السريع

### على Windows:
```bash
# انقر مرتين على الملف
run.bat
```

### على macOS/Linux:
```bash
# في Terminal
./run.sh
```

### يدوياً:
```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. تشغيل التطبيق
python app.py

# 3. فتح المتصفح
# اذهب إلى: http://localhost:5000
```

## 🔑 إعداد مفتاح API

1. اذهب إلى [Google AI Studio](https://makersuite.google.com/app/apikey)
2. أنشئ مفتاح API جديد
3. افتح ملف `app.py`
4. استبدل `API_KEY` بمفتاحك الخاص

## ✅ اختبار التطبيق

جرب هذه الأسئلة:
- "عندي صداع شديد"
- "إزاي أتعامل مع الحروق؟"
- "أعمل إيه لو حد اغمى عليه؟"

## 🆘 حل المشاكل الشائعة

### خطأ: Python غير موجود
```bash
# تثبيت Python من
https://python.org
```

### خطأ: مفتاح API غير صحيح
- تأكد من صحة مفتاح Google AI
- تأكد من تفعيل Gemini API

### خطأ: Port مستخدم
```bash
# غير البورت في app.py
app.run(debug=True, host='0.0.0.0', port=5001)
```

## 📱 الوصول من الهاتف

إذا كان الكمبيوتر والهاتف على نفس الشبكة:
```
http://[IP-ADDRESS]:5000
```

مثال:
```
http://*************:5000
```

## 🔒 الأمان

- لا تشارك مفتاح API مع أحد
- استخدم HTTPS في الإنتاج
- غير SECRET_KEY في الإنتاج
