"""
Configuration file for Medical Chatbot
"""

import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'medical-chatbot-secret-key-2024'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # Google AI Configuration
    GOOGLE_API_KEY = os.environ.get('GOOGLE_API_KEY') or 'AIzaSyBPs9sNSu0-4Z33TUEvHrVF6Mx1CKLj9zQ'
    GOOGLE_MODEL = os.environ.get('GOOGLE_MODEL') or 'gemini-2.5-flash-preview-05-20'
    
    # File Configuration
    MEMORY_FILE = 'memory.json'
    HISTORY_FILE = 'history.json'
    MAX_HISTORY_SIZE = 1000  # Maximum number of conversations to keep
    
    # Chat Configuration
    MAX_MESSAGE_LENGTH = 2000
    TYPING_DELAY = 1.0  # Seconds to simulate typing
    
    # Emergency Configuration
    EMERGENCY_KEYWORDS = [
        'طوارئ', 'emergency', 'نزيف', 'bleeding', 'اختناق', 'choking',
        'توقف القلب', 'cardiac arrest', 'فقدان الوعي', 'unconscious',
        'حادث', 'accident', 'سكتة', 'stroke', 'نوبة قلبية', 'heart attack',
        'تسمم', 'poisoning', 'حريق', 'fire', 'غرق', 'drowning'
    ]
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE = 30
    RATE_LIMIT_PER_HOUR = 200
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = 'medical_chatbot.log'
    
    # Security Configuration
    ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']
    
    # Medical Disclaimer
    MEDICAL_DISCLAIMER = "⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص."
    
    # System Instructions for AI
    SYSTEM_INSTRUCTION = """
You are a helpful and knowledgeable medical assistant. Your role is to:
- Provide clear, accurate, and concise medical information.
- Reply in Arabic if the user writes in Arabic, especially Egyptian dialect.
- Always include this disclaimer at the end: ⚠️ ده مش تشخيص طبي، لو الأعراض مستمرة أو زادت، لازم تروح لدكتور متخصص.

✅ If the user asks for a diet plan, ask about their health goal (e.g., weight loss, diabetes, anemia), dietary preference (e.g., vegetarian), and suggest a simple meal plan (breakfast, lunch, dinner). Make it affordable and relevant to the region.

✅ If the user shares lab results (e.g., CBC, blood sugar), explain the values in simple terms. Mention if it's high/low, what it might mean, and recommend seeing a doctor for full interpretation.

✅ Be always friendly, respectful, and keep things easy to understand.
"""

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Use environment variables for sensitive data in production
    GOOGLE_API_KEY = os.environ.get('GOOGLE_API_KEY')
    SECRET_KEY = os.environ.get('SECRET_KEY')
    
    # Enhanced security for production
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    GOOGLE_API_KEY = 'test-api-key'

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
