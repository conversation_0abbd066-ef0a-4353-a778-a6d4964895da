@echo off
echo ========================================
echo    المساعد الطبي الذكي
echo    AI Medical Assistant
echo ========================================
echo.

echo جاري التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python من https://python.org
    pause
    exit /b 1
)

echo جاري التحقق من المتطلبات...
if not exist "venv" (
    echo إنشاء البيئة الافتراضية...
    python -m venv venv
)

echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate

echo تثبيت المتطلبات...
pip install -r requirements.txt

echo.
echo ========================================
echo تشغيل المساعد الطبي الذكي...
echo يمكنك الوصول للتطبيق على:
echo http://localhost:5000
echo ========================================
echo.

python app.py

pause
