// DOM Elements
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const welcomeMessage = document.getElementById('welcomeMessage');
const typingIndicator = document.getElementById('typingIndicator');
const clearChatBtn = document.getElementById('clearChat');
const exportChatBtn = document.getElementById('exportChat');
const emergencyModal = document.getElementById('emergencyModal');
const loadingOverlay = document.getElementById('loadingOverlay');

// Chat state
let chatHistory = [];
let isTyping = false;

// Emergency keywords that trigger the emergency modal
const emergencyKeywords = [
    'طوارئ', 'emergency', 'نزيف', 'bleeding', 'اختناق', 'choking',
    'توقف القلب', 'cardiac arrest', 'فقدان الوعي', 'unconscious',
    'حادث', 'accident', 'سكتة', 'stroke', 'نوبة قلبية', 'heart attack'
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadChatHistory();
    adjustTextareaHeight();
});

// Event Listeners
function initializeEventListeners() {
    // Send message events
    sendBtn.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Input validation
    messageInput.addEventListener('input', function() {
        const hasText = this.value.trim().length > 0;
        sendBtn.disabled = !hasText;
        adjustTextareaHeight();
        
        // Check for emergency keywords
        if (hasText && containsEmergencyKeyword(this.value)) {
            showEmergencyModal();
        }
    });

    // Quick action buttons
    document.querySelectorAll('.quick-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const message = this.getAttribute('data-message');
            messageInput.value = message;
            sendBtn.disabled = false;
            sendMessage();
        });
    });

    // Clear chat
    clearChatBtn.addEventListener('click', clearChat);

    // Export chat
    exportChatBtn.addEventListener('click', exportChat);

    // Emergency modal
    document.getElementById('closeEmergency').addEventListener('click', hideEmergencyModal);
    document.getElementById('continueChat').addEventListener('click', hideEmergencyModal);

    // Close modal when clicking outside
    emergencyModal.addEventListener('click', function(e) {
        if (e.target === this) {
            hideEmergencyModal();
        }
    });
}

// Send message function
async function sendMessage() {
    const message = messageInput.value.trim();
    if (!message || isTyping) return;

    // Add user message to chat
    addMessage(message, 'user');
    messageInput.value = '';
    sendBtn.disabled = true;
    adjustTextareaHeight();

    // Hide welcome message
    if (welcomeMessage) {
        welcomeMessage.style.display = 'none';
    }

    // Show typing indicator
    showTypingIndicator();

    try {
        // Send message to backend
        const response = await fetch('/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message })
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const data = await response.json();
        
        // Hide typing indicator
        hideTypingIndicator();
        
        // Add bot response
        addMessage(data.response, 'bot');
        
    } catch (error) {
        console.error('Error:', error);
        hideTypingIndicator();
        
        // Add error message
        addMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'bot', true);
    }

    // Save chat history
    saveChatHistory();
}

// Add message to chat
function addMessage(text, sender, isError = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const content = document.createElement('div');
    content.className = 'message-content';
    
    if (isError) {
        content.style.background = 'linear-gradient(45deg, #dc3545, #c82333)';
        content.style.color = 'white';
    }

    // Format message text (convert line breaks, etc.)
    content.innerHTML = formatMessageText(text);

    const time = document.createElement('div');
    time.className = 'message-time';
    time.textContent = new Date().toLocaleTimeString('ar-EG', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });

    content.appendChild(time);
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);

    chatMessages.appendChild(messageDiv);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // Add to chat history
    chatHistory.push({
        text: text,
        sender: sender,
        timestamp: new Date().toISOString(),
        isError: isError
    });

    // Animate message appearance
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(20px)';
    setTimeout(() => {
        messageDiv.style.transition = 'all 0.3s ease';
        messageDiv.style.opacity = '1';
        messageDiv.style.transform = 'translateY(0)';
    }, 100);
}

// Format message text
function formatMessageText(text) {
    return text
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
}

// Show/Hide typing indicator
function showTypingIndicator() {
    isTyping = true;
    typingIndicator.style.display = 'flex';
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function hideTypingIndicator() {
    isTyping = false;
    typingIndicator.style.display = 'none';
}

// Adjust textarea height
function adjustTextareaHeight() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
}

// Emergency modal functions
function containsEmergencyKeyword(text) {
    const lowerText = text.toLowerCase();
    return emergencyKeywords.some(keyword => lowerText.includes(keyword));
}

function showEmergencyModal() {
    emergencyModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function hideEmergencyModal() {
    emergencyModal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Clear chat function
function clearChat() {
    if (confirm('هل أنت متأكد من مسح المحادثة؟')) {
        chatMessages.innerHTML = '';
        chatHistory = [];
        welcomeMessage.style.display = 'block';
        localStorage.removeItem('medicalChatHistory');
    }
}

// Export chat function
function exportChat() {
    if (chatHistory.length === 0) {
        alert('لا توجد محادثة لتصديرها');
        return;
    }

    const chatText = chatHistory.map(msg => {
        const sender = msg.sender === 'user' ? 'المستخدم' : 'المساعد الطبي';
        const time = new Date(msg.timestamp).toLocaleString('ar-EG');
        return `[${time}] ${sender}: ${msg.text}`;
    }).join('\n\n');

    const blob = new Blob([chatText], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `medical-chat-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Save/Load chat history
function saveChatHistory() {
    localStorage.setItem('medicalChatHistory', JSON.stringify(chatHistory));
}

function loadChatHistory() {
    const saved = localStorage.getItem('medicalChatHistory');
    if (saved) {
        chatHistory = JSON.parse(saved);
        
        // Restore messages
        chatHistory.forEach(msg => {
            addMessageToDOM(msg.text, msg.sender, msg.isError);
        });

        if (chatHistory.length > 0) {
            welcomeMessage.style.display = 'none';
        }
    }
}

// Add message to DOM without adding to history (for loading saved messages)
function addMessageToDOM(text, sender, isError = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const content = document.createElement('div');
    content.className = 'message-content';
    
    if (isError) {
        content.style.background = 'linear-gradient(45deg, #dc3545, #c82333)';
        content.style.color = 'white';
    }

    content.innerHTML = formatMessageText(text);

    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);
    chatMessages.appendChild(messageDiv);
}

// Show loading overlay
function showLoading() {
    loadingOverlay.style.display = 'flex';
}

function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// Handle offline/online status
window.addEventListener('online', function() {
    addMessage('تم استعادة الاتصال بالإنترنت', 'bot');
});

window.addEventListener('offline', function() {
    addMessage('تم فقدان الاتصال بالإنترنت. بعض الميزات قد لا تعمل.', 'bot', true);
});

// Service Worker registration (for PWA functionality)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}
