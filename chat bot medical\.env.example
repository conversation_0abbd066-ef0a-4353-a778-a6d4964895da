# Medical Chatbot Environment Variables
# Copy this file to .env and fill in your actual values

# Flask Configuration
FLASK_CONFIG=development
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here

# Google AI Configuration
GOOGLE_API_KEY=your-google-ai-api-key-here
GOOGLE_MODEL=gemini-2.5-flash-preview-05-20

# Logging Configuration
LOG_LEVEL=INFO

# Server Configuration
HOST=0.0.0.0
PORT=5000

# Security (for production)
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
