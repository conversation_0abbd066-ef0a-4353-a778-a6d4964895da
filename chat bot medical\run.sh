#!/bin/bash

echo "========================================"
echo "    المساعد الطبي الذكي"
echo "    AI Medical Assistant"
echo "========================================"
echo

echo "جاري التحقق من Python..."
if ! command -v python3 &> /dev/null; then
    echo "خطأ: Python غير مثبت على النظام"
    echo "يرجى تثبيت Python من https://python.org"
    exit 1
fi

echo "جاري التحقق من المتطلبات..."
if [ ! -d "venv" ]; then
    echo "إنشاء البيئة الافتراضية..."
    python3 -m venv venv
fi

echo "تفعيل البيئة الافتراضية..."
source venv/bin/activate

echo "تثبيت المتطلبات..."
pip install -r requirements.txt

echo
echo "========================================"
echo "تشغيل المساعد الطبي الذكي..."
echo "يمكنك الوصول للتطبيق على:"
echo "http://localhost:5000"
echo "========================================"
echo

python3 app.py
