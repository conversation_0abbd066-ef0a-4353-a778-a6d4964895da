/*
 * AI Medical Assistant - Unified Bootstrap Theme
 * This file contains custom styling on top of Bootstrap 5
 * Unified design system for consistent UI across all pages
 */

:root {
  /* Primary color scheme - Medical blue theme */
  --bs-primary: #2563eb;
  --bs-primary-rgb: 37, 99, 235;
  --bs-primary-light: #60a5fa;
  --bs-primary-dark: #1d4ed8;

  /* Secondary colors */
  --bs-secondary: #f97316;
  --bs-secondary-rgb: 249, 115, 22;
  --bs-success: #10b981;
  --bs-success-rgb: 16, 185, 129;
  --bs-info: #0ea5e9;
  --bs-info-rgb: 14, 165, 233;
  --bs-warning: #f59e0b;
  --bs-warning-rgb: 245, 158, 11;
  --bs-danger: #ef4444;
  --bs-danger-rgb: 239, 68, 68;

  /* Neutral colors */
  --bs-light: #f8fafc;
  --bs-light-rgb: 248, 250, 252;
  --bs-dark: #1e293b;
  --bs-dark-rgb: 30, 41, 59;
  --bs-gray-100: #f1f5f9;
  --bs-gray-200: #e2e8f0;
  --bs-gray-300: #cbd5e1;
  --bs-gray-400: #94a3b8;
  --bs-gray-500: #64748b;
  --bs-gray-600: #475569;
  --bs-gray-700: #334155;
  --bs-gray-800: #1e293b;
  --bs-gray-900: #0f172a;

  /* Typography */
  --bs-body-font-family: 'Plus Jakarta Sans', 'Inter', system-ui, -apple-system, sans-serif;
  --bs-body-font-size: 1rem;
  --bs-body-line-height: 1.6;
  --bs-body-color: #1e293b;
  --bs-body-bg: #f8fafc;

  /* Custom shadows - Enhanced for depth */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Border radius system */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-3xl: 1.5rem;

  /* Spacing system */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ========== GLOBAL STYLES ========== */

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  padding-top: 4rem;
  overflow-x: hidden;
  font-family: var(--bs-body-font-family);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography improvements */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.75rem;
  color: var(--bs-dark);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

/* Enhanced button styles */
.btn {
  font-weight: 500;
  letter-spacing: 0.3px;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-normal);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

.btn-primary {
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--bs-primary-dark) 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline-primary {
  border: 2px solid var(--bs-primary);
  color: var(--bs-primary);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
  transform: translateY(-1px);
}

/* Link improvements */
a {
  color: var(--bs-primary);
  text-decoration: none;
  transition: all var(--transition-fast);
}

a:hover {
  color: var(--bs-primary-dark);
  text-decoration: underline;
}

/* Form improvements */
.form-control, .form-select {
  border: 2px solid var(--bs-gray-200);
  border-radius: var(--border-radius-lg);
  padding: 0.75rem 1rem;
  transition: all var(--transition-normal);
  font-size: 1rem;
}

.form-control:focus, .form-select:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.15);
  outline: none;
}

.form-label {
  font-weight: 600;
  color: var(--bs-gray-700);
  margin-bottom: 0.5rem;
}

/* Card improvements */
.card {
  border: none;
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, var(--bs-light) 0%, white 100%);
  border-bottom: 1px solid var(--bs-gray-200);
  font-weight: 600;
}

/* Utility classes */
.text-gradient {
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-info) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark) 100%);
}

.bg-gradient-light {
  background: linear-gradient(135deg, var(--bs-light) 0%, white 100%);
}

.rounded-4 {
  border-radius: var(--border-radius-2xl) !important;
}

.rounded-5 {
  border-radius: var(--border-radius-3xl) !important;
}

.shadow-soft {
  box-shadow: var(--shadow-md);
}

.shadow-strong {
  box-shadow: var(--shadow-lg);
}

/* Navbar styling */
.navbar {
  background-color: rgba(255, 255, 255, 0.98);
  padding: 1rem 0;
  transition: all 0.3s ease;
}

.navbar-brand {
  font-size: 1.25rem;
}

.nav-link {
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: var(--bs-primary);
  transition: width 0.3s ease;
}

.nav-link.active:after,
.nav-link:hover:after {
  width: 80%;
}

/* Hero Section */
.hero {
  background-image: linear-gradient(rgba(30, 41, 59, 0.85), rgba(30, 41, 59, 0.9)), url('background.jpg');
  background-size: cover;
  background-position: center;
  min-height: 650px;
  padding: 6rem 0;
}

.hero-image-container {
  transform: translateY(-20px);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(-20px); }
  50% { transform: translateY(0); }
  100% { transform: translateY(-20px); }
}

/* Feature Icons */
.feature-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  font-size: 1.5rem;
}

/* Step Numbers */
.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  font-weight: 700;
  font-size: 1.25rem;
}

/* Card hover effect */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

/* Testimonial styling */
.testimonial-avatar {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

/* Forms */
.form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Button styles */
.btn-primary {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

/* Footer styles */
.footer {
  background-color: #0f172a;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .hero {
    min-height: 500px;
    text-align: center;
  }
}

@media (max-width: 768px) {
  body {
    padding-top: 3.5rem;
  }
  
  .navbar {
    padding: 0.5rem 0;
  }
  
  .hero {
    padding: 4rem 0;
  }
  
  .hero h1 {
    font-size: 2.25rem;
  }
}

/* Custom animations */
.fade-in {
  animation: fadeIn 1s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading animation */
.loader {
  width: 48px;
  height: 48px;
  border: 5px solid rgba(var(--bs-primary-rgb), 0.2);
  border-top: 5px solid var(--bs-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom utilities */
.rounded-4 {
  border-radius: 0.75rem !important;
}

.rounded-5 {
  border-radius: 1rem !important;
}

.py-md-6 {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important;
}

.h-400 {
  height: 400px;
}

/* Diagnosis form styles */
.diagnosis-form-container {
  background-color: white;
  border-radius: 1rem;
  box-shadow: var(--shadow-lg);
}

.tips-card {
  border-left: 4px solid var(--bs-info);
  background-color: rgba(var(--bs-info-rgb), 0.1);
}

/* Results page styles */
.condition-card {
  border-left: 4px solid var(--bs-primary);
  transition: transform 0.3s ease;
}

.condition-card:hover {
  transform: translateX(5px);
}

.probability-badge {
  font-weight: 500;
  padding: 0.35rem 0.75rem;
  border-radius: 1rem;
}

.probability-high {
  background-color: var(--bs-danger);
  color: white;
}

.probability-medium {
  background-color: var(--bs-warning);
  color: white;
}

.probability-low {
  background-color: var(--bs-info);
  color: white;
}

/* Medical disclaimer styling */
.medical-disclaimer {
  background-color: rgba(var(--bs-danger-rgb), 0.1);
  border-left: 4px solid var(--bs-danger);
}

/* ========== ENHANCED COMPONENTS ========== */

/* Enhanced hover effects */
.hover-lift {
  transition: all var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

/* Enhanced navbar */
.navbar {
  background-color: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all var(--transition-normal);
}

.navbar-brand {
  font-size: 1.25rem;
  font-weight: 700;
}

.nav-link {
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  transition: all var(--transition-normal);
  position: relative;
  border-radius: var(--border-radius-lg);
}

.nav-link:hover {
  background-color: var(--bs-gray-100);
  color: var(--bs-primary) !important;
}

.nav-link.active {
  background-color: var(--bs-gray-100);
  color: var(--bs-primary) !important;
  font-weight: 600;
}

/* Enhanced hero section */
.hero {
  background-image: linear-gradient(rgba(30, 41, 59, 0.85), rgba(30, 41, 59, 0.9)), url('background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 650px;
  padding: 6rem 0;
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(37, 99, 235, 0.1) 0%, rgba(14, 165, 233, 0.1) 100%);
  pointer-events: none;
}

.hero-image-container {
  transform: translateY(-20px);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(-20px); }
  50% { transform: translateY(0); }
}

/* Enhanced feature icons */
.feature-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  font-size: 1.5rem;
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-info) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

/* Enhanced step numbers */
.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  font-weight: 700;
  font-size: 1.25rem;
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-info) 100%);
  color: white;
  border-radius: 50%;
  box-shadow: var(--shadow-md);
}

/* Enhanced testimonial styling */
.testimonial-avatar {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-info) 100%);
  color: white;
  border-radius: 50%;
}

/* Enhanced loading animation */
.loader {
  width: 48px;
  height: 48px;
  border: 5px solid rgba(var(--bs-primary-rgb), 0.2);
  border-top: 5px solid var(--bs-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced diagnosis form styles */
.diagnosis-form-container {
  background-color: white;
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--bs-gray-200);
}

.tips-card {
  border-left: 4px solid var(--bs-info);
  background: linear-gradient(135deg, rgba(var(--bs-info-rgb), 0.1) 0%, rgba(var(--bs-info-rgb), 0.05) 100%);
  border-radius: var(--border-radius-lg);
}

/* Enhanced results page styles */
.condition-card {
  border-left: 4px solid var(--bs-primary);
  transition: all var(--transition-normal);
  background: white;
  border-radius: var(--border-radius-lg);
}

.condition-card:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow-lg);
}

.probability-badge {
  font-weight: 600;
  padding: 0.35rem 0.75rem;
  border-radius: var(--border-radius-2xl);
  font-size: 0.875rem;
}

.probability-high {
  background: linear-gradient(135deg, var(--bs-danger) 0%, #dc2626 100%);
  color: white;
}

.probability-medium {
  background: linear-gradient(135deg, var(--bs-warning) 0%, #f59e0b 100%);
  color: white;
}

.probability-low {
  background: linear-gradient(135deg, var(--bs-info) 0%, #0ea5e9 100%);
  color: white;
}

/* Enhanced footer */
.footer {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
}

/* Enhanced modal styles */
.modal-content {
  border: none;
  box-shadow: var(--shadow-xl);
}

.modal-header {
  border-bottom: 1px solid var(--bs-gray-200);
}

/* Enhanced alert positioning */
.alert {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
}

/* Enhanced responsive design */
@media (max-width: 992px) {
  .hero {
    min-height: 500px;
    text-align: center;
    background-attachment: scroll;
  }

  .hero-image-container {
    animation: none;
    transform: none;
  }
}

@media (max-width: 768px) {
  body {
    padding-top: 3.5rem;
  }

  .navbar {
    padding: 0.5rem 0;
  }

  .hero {
    padding: 4rem 0;
  }

  .hero h1 {
    font-size: 2.25rem;
  }

  .feature-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }

  .step-number {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }
}

/* Print styles */
@media print {
  .navbar,
  .footer,
  .btn,
  .modal {
    display: none !important;
  }

  body {
    padding-top: 0;
  }

  .container {
    max-width: none;
  }
}