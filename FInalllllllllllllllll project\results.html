<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج التشخيص - مساعد طبي ذكي</title>
    <meta name="description" content="نتائج التشخيص بالذكاء الاصطناعي والتوصيات الطبية">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style-bootstrap.css">
    <style>
        body { font-family: 'Cairo', sans-serif; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <i class="fas fa-heartbeat text-primary me-2"></i>
                <span class="fw-bold">مساعد طبي ذكي</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="diagnosis.html">Diagnosis</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="appointments.html">Book Appointment</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="About.Html">About</a>
                    </li>
                    <li class="nav-item ms-lg-3 mt-2 mt-lg-0">
                        <button class="btn btn-primary rounded-pill px-4" id="loginBtn">Login</button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content rounded-4 shadow">
                <div class="modal-header p-4 pb-0 border-bottom-0">
                    <ul class="nav nav-tabs nav-fill w-100" id="authTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#login-tab-pane" type="button" role="tab" aria-controls="login-tab-pane" aria-selected="true">Login</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="register-tab" data-bs-toggle="tab" data-bs-target="#register-tab-pane" type="button" role="tab" aria-controls="register-tab-pane" aria-selected="false">Register</button>
                        </li>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4 pt-0">
                    <div class="tab-content" id="authTabContent">
                        <!-- Login Form -->
                        <div class="tab-pane fade show active" id="login-tab-pane" role="tabpanel" aria-labelledby="login-tab" tabindex="0">
                            <form id="loginForm" class="mt-4">
                                <div class="mb-3">
                                    <label for="loginEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="loginEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="loginPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="loginPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mt-3 rounded-pill">Login</button>
                            </form>
                        </div>
                        <!-- Register Form -->
                        <div class="tab-pane fade" id="register-tab-pane" role="tabpanel" aria-labelledby="register-tab" tabindex="0">
                            <form id="registerForm" class="mt-4">
                                <div class="mb-3">
                                    <label for="registerName" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="registerName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="registerEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="registerPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100 mt-3 rounded-pill">Register</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="py-5 py-md-6 mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="bg-white shadow-lg rounded-4 p-4 p-md-5">
                        <div class="text-center mb-4">
                            <h1 class="mb-2">Your Diagnosis Results</h1>
                            <p class="text-muted">Based on the symptoms you provided</p>
                        </div>

                        <div class="medical-disclaimer p-4 rounded-4 mb-5">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                <h5 class="mb-0 fw-bold">Medical Disclaimer</h5>
                            </div>
                            <p class="mb-0">This analysis is provided for informational purposes only and is not a substitute for professional medical advice. Please consult with a healthcare provider for proper diagnosis and treatment.</p>
                        </div>

                        <div id="results-content">
                            <!-- High probability condition -->
                            <div class="condition-card rounded-4 p-4 mb-4 shadow-sm border-0">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h3 class="h5 fw-bold mb-1">Common Cold</h3>
                                        <p class="text-muted mb-0">A viral infection of the upper respiratory tract</p>
                                    </div>
                                    <span class="probability-badge probability-high">85% match</span>
                                </div>
                                <div class="mt-3 mb-2">
                                    <h4 class="h6 fw-bold">Recommendations:</h4>
                                    <ul class="mb-0">
                                        <li>Rest and stay hydrated</li>
                                        <li>Take over-the-counter cold medications</li>
                                        <li>Monitor symptoms for worsening</li>
                                    </ul>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-3" type="button" data-bs-toggle="collapse" data-bs-target="#coldDetails" aria-expanded="false" aria-controls="coldDetails">
                                    Show details
                                </button>
                                <div class="collapse mt-3" id="coldDetails">
                                    <div class="p-3 bg-light rounded-3">
                                        <h5 class="h6 fw-bold">Common symptoms:</h5>
                                        <p>Runny or stuffy nose, sore throat, cough, congestion, mild body aches, sneezing, low-grade fever, generally feeling unwell</p>
                                        <h5 class="h6 fw-bold">When to seek medical attention:</h5>
                                        <p class="mb-0">If symptoms last more than 10 days, you have severe symptoms, or if you have underlying health conditions that might be affected.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Medium probability condition -->
                            <div class="condition-card rounded-4 p-4 mb-4 shadow-sm border-0">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <div>
                                        <h3 class="h5 fw-bold mb-1">Seasonal Allergies</h3>
                                        <p class="text-muted mb-0">An allergic response to environmental triggers</p>
                                    </div>
                                    <span class="probability-badge probability-medium">65% match</span>
                                </div>
                                <div class="mt-3 mb-2">
                                    <h4 class="h6 fw-bold">Recommendations:</h4>
                                    <ul class="mb-0">
                                        <li>Avoid known allergens</li>
                                        <li>Consider antihistamines</li>
                                        <li>Use air purifiers</li>
                                    </ul>
                                </div>
                                <button class="btn btn-sm btn-outline-primary mt-3" type="button" data-bs-toggle="collapse" data-bs-target="#allergyDetails" aria-expanded="false" aria-controls="allergyDetails">
                                    Show details
                                </button>
                                <div class="collapse mt-3" id="allergyDetails">
                                    <div class="p-3 bg-light rounded-3">
                                        <h5 class="h6 fw-bold">Common symptoms:</h5>
                                        <p>Sneezing, itchy nose, itchy eyes, watery eyes, runny nose, congestion</p>
                                        <h5 class="h6 fw-bold">Common triggers:</h5>
                                        <p class="mb-0">Pollen, dust mites, pet dander, mold, certain foods</p>
                                    </div>
                                </div>
                            </div>

                            <!-- New Feature: Health Timeline -->
                            <div class="p-4 rounded-4 bg-light mt-5">
                                <h3 class="h5 fw-bold mb-3">Recommended Next Steps</h3>
                                <div class="position-relative ps-3">
                                    <div class="timeline-line"></div>
                                    
                                    <div class="timeline-item">
                                        <h4 class="h6 fw-bold mb-1">Immediate</h4>
                                        <p>Follow the rest and hydration recommendations. Take over-the-counter medications as suggested.</p>
                                    </div>
                                    
                                    <div class="timeline-item">
                                        <h4 class="h6 fw-bold mb-1">Within 3 Days</h4>
                                        <p>If symptoms persist or worsen, schedule an appointment with your primary care physician.</p>
                                    </div>
                                    
                                    <div class="timeline-item">
                                        <h4 class="h6 fw-bold mb-1">Within 1 Week</h4>
                                        <p>Log back into the app to update your symptom progress and receive adjusted recommendations.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex flex-column flex-sm-row justify-content-center gap-3 mt-5">
                            <a href="diagnosis.html" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>
                                New Diagnosis
                            </a>
                            <button id="saveResultsBtn" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Save Results
                            </button>
                            <a href="#" class="btn btn-outline-secondary">
                                <i class="fas fa-print me-2"></i>
                                Print
                            </a>
                        </div>

                        <!-- Share results component -->
                        <div class="text-center mt-5">
                            <p class="fw-medium mb-3">Share your results with your healthcare provider</p>
                            <div class="d-flex justify-content-center gap-3">
                                <button class="btn btn-sm btn-outline-primary rounded-pill px-3">
                                    <i class="far fa-envelope me-2"></i>Email
                                </button>
                                <button class="btn btn-sm btn-outline-primary rounded-pill px-3">
                                    <i class="fas fa-share-alt me-2"></i>Share
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container py-4">
            <div class="row g-4">
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="About.Html" class="link-light text-decoration-none">About Us</a></li>
                        <li class="mb-2"><a href="diagnosis.html" class="link-light text-decoration-none">Get Diagnosis</a></li>
                        <li class="mb-2"><a href="privacy.html" class="link-light text-decoration-none">Privacy Policy</a></li>
                        <li class="mb-2"><a href="terms.html" class="link-light text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Connect With Us</h5>
                    <div class="d-flex gap-3">
                        <a href="#" class="link-light fs-5" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="link-light fs-5" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Contact</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i>+****************</li>
                        <li class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>123 Health Street, Medical City</li>
                    </ul>
                </div>
                <div class="col-md-12 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Subscribe to Our Newsletter</h5>
                    <form class="d-flex">
                        <input type="email" class="form-control" placeholder="Your email">
                        <button type="submit" class="btn btn-primary ms-2">Subscribe</button>
                    </form>
                </div>
            </div>
            <hr class="my-4 border-light">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; 2024 AI Medical Assistant. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end mt-3 mt-md-0">
                    <small>Made with <i class="fas fa-heart text-danger"></i> for better healthcare</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Arabic Localization -->
    <script src="js/arabic-localization.js"></script>
    <!-- Custom JS -->
    <script src="js/main-bootstrap.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Save Results Button functionality
            const saveResultsBtn = document.getElementById('saveResultsBtn');
            if (saveResultsBtn) {
                saveResultsBtn.addEventListener('click', function() {
                    alert('Results have been saved to your account!');
                });
            }
            
            // Initialize tooltips
            const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl))
        });
    </script>

    <style>
    /* Timeline styling */
    .timeline-line {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 2px;
        background-color: var(--bs-primary);
    }
    
    .timeline-item {
        position: relative;
        padding-left: 20px;
        margin-bottom: 1.5rem;
    }
    
    .timeline-item:before {
        content: '';
        position: absolute;
        left: -8px;
        top: 6px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: white;
        border: 2px solid var(--bs-primary);
    }
    
    .timeline-item:last-child {
        margin-bottom: 0;
    }
    </style>
</body>
</html> 