<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حجز موعد - مساعد طبي ذكي</title>
    <meta name="description" content="احجز موعدك مع أفضل الأطباء المتخصصين">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style-bootstrap.css">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .specialty-card { transition: all 0.3s ease; cursor: pointer; }
        .specialty-card:hover { transform: translateY(-5px); box-shadow: 0 10px 25px rgba(0,0,0,0.15); }
        .specialty-icon { font-size: 3rem; margin-bottom: 1rem; }
        .step-indicator { display: flex; justify-content: center; margin-bottom: 2rem; }
        .step { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 10px; }
        .step.active { background: var(--bs-primary); color: white; }
        .step.completed { background: var(--bs-success); color: white; }
        .step.pending { background: var(--bs-gray-300); color: var(--bs-gray-600); }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.html">
                <i class="fas fa-heartbeat text-primary me-2"></i>
                <span class="fw-bold">مساعد طبي ذكي</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item"><a class="nav-link" href="index.html">الرئيسية</a></li>
                    <li class="nav-item"><a class="nav-link" href="diagnosis.html">التشخيص</a></li>
                    <li class="nav-item"><a class="nav-link active" href="appointments.html">حجز موعد</a></li>
                    <li class="nav-item"><a class="nav-link" href="contact.html">اتصل بنا</a></li>
                    <li class="nav-item"><a class="nav-link" href="About.Html">عن المشروع</a></li>
                    <li class="nav-item ms-lg-3 mt-2 mt-lg-0">
                        <button class="btn btn-primary rounded-pill px-4" id="loginBtn">تسجيل الدخول</button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="py-5 py-md-6 mt-5">
        <div class="container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step pending" id="step2">2</div>
                <div class="step pending" id="step3">3</div>
                <div class="step pending" id="step4">4</div>
            </div>

            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold text-gradient mb-3">حجز موعد مع طبيب</h1>
                <p class="lead text-muted">اختر التخصص المناسب لحالتك الصحية</p>
            </div>

            <!-- Specialties Grid -->
            <div class="row g-4" id="specialtiesGrid">
                <div class="col-md-6 col-lg-4">
                    <div class="card specialty-card border-0 shadow-sm rounded-4 h-100" data-specialty="cardiology">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-heart specialty-icon text-danger"></i>
                            <h5 class="fw-bold mb-2">أمراض القلب</h5>
                            <p class="text-muted mb-3">تشخيص وعلاج أمراض القلب والأوعية الدموية</p>
                            <small class="text-primary">5 أطباء متاحين</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 col-lg-4">
                    <div class="card specialty-card border-0 shadow-sm rounded-4 h-100" data-specialty="neurology">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-brain specialty-icon text-info"></i>
                            <h5 class="fw-bold mb-2">أمراض الأعصاب</h5>
                            <p class="text-muted mb-3">تشخيص وعلاج اضطرابات الجهاز العصبي</p>
                            <small class="text-primary">3 أطباء متاحين</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-4">
                    <div class="card specialty-card border-0 shadow-sm rounded-4 h-100" data-specialty="orthopedics">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-bone specialty-icon text-warning"></i>
                            <h5 class="fw-bold mb-2">العظام</h5>
                            <p class="text-muted mb-3">علاج إصابات وأمراض العظام والمفاصل</p>
                            <small class="text-primary">4 أطباء متاحين</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-4">
                    <div class="card specialty-card border-0 shadow-sm rounded-4 h-100" data-specialty="dermatology">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-user-md specialty-icon text-success"></i>
                            <h5 class="fw-bold mb-2">الجلدية</h5>
                            <p class="text-muted mb-3">علاج أمراض الجلد والشعر والأظافر</p>
                            <small class="text-primary">6 أطباء متاحين</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-4">
                    <div class="card specialty-card border-0 shadow-sm rounded-4 h-100" data-specialty="pediatrics">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-baby specialty-icon text-primary"></i>
                            <h5 class="fw-bold mb-2">طب الأطفال</h5>
                            <p class="text-muted mb-3">رعاية صحية شاملة للأطفال والمراهقين</p>
                            <small class="text-primary">7 أطباء متاحين</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 col-lg-4">
                    <div class="card specialty-card border-0 shadow-sm rounded-4 h-100" data-specialty="general">
                        <div class="card-body text-center p-4">
                            <i class="fas fa-stethoscope specialty-icon text-secondary"></i>
                            <h5 class="fw-bold mb-2">طب عام</h5>
                            <p class="text-muted mb-3">فحص شامل وتشخيص أولي للحالات العامة</p>
                            <small class="text-primary">8 أطباء متاحين</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="d-flex justify-content-between mt-5">
                <a href="index.html" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للرئيسية
                </a>
                <button class="btn btn-primary" id="nextBtn" disabled>
                    التالي
                    <i class="fas fa-arrow-left ms-2"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container py-4">
            <div class="row g-4">
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="About.Html" class="link-light text-decoration-none">عن المشروع</a></li>
                        <li class="mb-2"><a href="diagnosis.html" class="link-light text-decoration-none">التشخيص</a></li>
                        <li class="mb-2"><a href="appointments.html" class="link-light text-decoration-none">حجز موعد</a></li>
                    </ul>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">تواصل معنا</h5>
                    <div class="d-flex gap-3">
                        <a href="#" class="link-light fs-5"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="link-light fs-5"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="link-light fs-5"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="col-md-4 col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">معلومات التواصل</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i>+****************</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4 border-light">
            <div class="text-center">
                <p class="mb-0">&copy; 2024 مساعد طبي ذكي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Arabic Localization -->
    <script src="js/arabic-localization.js"></script>
    <!-- Appointments Fix JS -->
    <script src="js/appointments-fix.js"></script>
    <script>
        let selectedSpecialty = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            const specialtyCards = document.querySelectorAll('.specialty-card');
            const nextBtn = document.getElementById('nextBtn');
            
            specialtyCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Remove previous selection
                    specialtyCards.forEach(c => c.classList.remove('border-primary', 'bg-light'));
                    
                    // Add selection to clicked card
                    this.classList.add('border-primary', 'bg-light');
                    selectedSpecialty = this.dataset.specialty;
                    
                    // Enable next button
                    nextBtn.disabled = false;
                });
            });
            
            nextBtn.addEventListener('click', function() {
                if (selectedSpecialty) {
                    window.location.href = `doctors.html?specialty=${selectedSpecialty}`;
                }
            });
        });
    </script>
</body>
</html>
